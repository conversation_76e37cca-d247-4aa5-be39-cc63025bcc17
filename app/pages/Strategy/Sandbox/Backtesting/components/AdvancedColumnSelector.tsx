import React, { useState, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ColumnStatistics } from '@/app/types';
import { availableTables } from '../../SampleSandboxData';
import { ColumnStats } from './ColumnStats';

interface ColumnInfo {
  column: string;
  description?: string;
  statistics?: ColumnStatistics;
}

interface AdvancedColumnSelectorProps {
  selectedTable: string;
  selectedColumns: ColumnInfo[];
  onColumnsChange: (columns: ColumnInfo[]) => void;
  onColumnStatsRequest?: (column: string) => Promise<ColumnStatistics>;
}

export const AdvancedColumnSelector: React.FC<AdvancedColumnSelectorProps> = ({
  selectedTable,
  selectedColumns,
  onColumnsChange,
  onColumnStatsRequest
}) => {
  const [loadingStats, setLoadingStats] = useState<Record<string, boolean>>({});
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const selectedTableData = availableTables.find(t => t.id === selectedTable);
  const availableColumns = selectedTableData 
    ? Object.keys(selectedTableData.schema.shape).filter(col => 
        !selectedColumns.some(selected => selected.column === col)
      )
    : [];

  const handleColumnSelect = useCallback((column: string) => {
    setSelectedColumn(column);
  }, []);

  const handleAddColumn = useCallback(() => {
    if (!selectedColumn) return;

    const description = (selectedTableData?.schema.shape as any)[selectedColumn]?._def.description;
    const newColumn: ColumnInfo = { column: selectedColumn, description };
    
    // If we have a stats request function, fetch stats first, then update once
    if (onColumnStatsRequest) {
      setLoadingStats(prev => ({ ...prev, [selectedColumn]: true }));
      onColumnStatsRequest(selectedColumn)
        .then(stats => {
          // Only update once with the complete data
          const updatedColumns = [...selectedColumns, { ...newColumn, statistics: stats }];
          onColumnsChange(updatedColumns);
        })
        .finally(() => {
          setLoadingStats(prev => ({ ...prev, [selectedColumn]: false }));
          setSelectedColumn(''); // Reset selection after adding
        });
    } else {
      // If no stats request, just update once
      const updatedColumns = [...selectedColumns, newColumn];
      onColumnsChange(updatedColumns);
      setSelectedColumn(''); // Reset selection after adding
    }
  }, [selectedColumn, selectedColumns, onColumnsChange, onColumnStatsRequest, selectedTableData]);

  const handleRemoveColumn = useCallback((column: string) => {
    onColumnsChange(selectedColumns.filter(col => col.column !== column));
  }, [selectedColumns, onColumnsChange]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Select
          value={selectedColumn}
          onValueChange={handleColumnSelect}
          disabled={availableColumns.length === 0}
        >
          <SelectTrigger className="w-full">
            <SelectValue>
              {selectedColumn ? (
                <div className="flex items-center">
                  <span className="truncate">{selectedColumn}</span>
                  {selectedTableData?.schema.shape && 
                   selectedColumn in selectedTableData.schema.shape && (
                    <span className="ml-2 text-sm text-muted-foreground truncate">
                      ({(selectedTableData.schema.shape as any)[selectedColumn]?._def.description || ''})
                    </span>
                  )}
                </div>
              ) : (
                "Select a column to analyze"
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {availableColumns.map(column => (
              <SelectItem key={column} value={column}>
                <div className="space-y-1">
                  <div className="font-medium">{column}</div>
                  <div className="text-sm text-muted-foreground">
                    {(selectedTableData?.schema.shape as any)[column]?._def.description || ''}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          size="icon"
          onClick={handleAddColumn}
          disabled={!selectedColumn}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-2">
        {selectedColumns.map(({ column, description, statistics }) => (
          <ColumnStats
            key={column}
            column={column}
            description={description}
            stats={statistics}
            isLoading={loadingStats[column]}
            onRemove={() => handleRemoveColumn(column)}
          />
        ))}
      </div>
    </div>
  );
}; 